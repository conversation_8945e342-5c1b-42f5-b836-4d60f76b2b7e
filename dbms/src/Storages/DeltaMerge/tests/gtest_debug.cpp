// Copyright 2024 PingCAP, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <Columns/ColumnArray.h>
#include <Columns/ColumnNullable.h>
#include <Columns/ColumnsNumber.h>
#include <Common/Checksum.h>
#include <Core/ColumnWithTypeAndName.h>
#include <IO/Compression/CompressionMethod.h>
#include <Interpreters/Context.h>
#include <Storages/DeltaMerge/DMChecksumConfig.h>
#include <Storages/DeltaMerge/DMContext.h>
#include <Storages/DeltaMerge/File/DMFileBlockInputStream.h>
#include <Storages/DeltaMerge/File/DMFileBlockOutputStream.h>
#include <Storages/DeltaMerge/RowKeyRange.h>
#include <Storages/DeltaMerge/ScanContext.h>
#include <Storages/DeltaMerge/tests/DMTestEnv.h>
#include <TestUtils/FunctionTestUtils.h>
#include <TestUtils/InputStreamTestUtils.h>
#include <TestUtils/TiFlashStorageTestBasic.h>
#include <gtest/gtest.h>

namespace DB::DM::tests
{

class DMFileDebugTest : public DB::base::TiFlashStorageTestBasic
{
public:
    DMFileDebugTest() = default;

protected:
    void SetUp() override
    {
        TiFlashStorageTestBasic::SetUp();

        // Configure compression and checksum settings
        auto & settings = db_context->getSettingsRef();
        settings.dt_compression_method = CompressionMethod::LZ4;
        settings.dt_checksum_algorithm = ChecksumAlgo::XXH3;

        // Create DMFile with explicit checksum configuration
        auto checksum_config = DMChecksumConfig(
            {}, // embedded_checksum
            TIFLASH_DEFAULT_CHECKSUM_FRAME_SIZE, // checksum_frame_length
            ChecksumAlgo::XXH3, // checksum_algorithm
            {} // debug_info (will use defaults)
        );

        dm_file = DMFile::create(1, getTemporaryPath(), std::make_optional<DMChecksumConfig>(checksum_config));
    }

    void TearDown() override
    {
        dm_file.reset();
        if (dmf2)
            dmf2.reset();
        TiFlashStorageTestBasic::TearDown();
    }

    Context & dbContext() { return *db_context; }

protected:
    DMFilePtr dm_file;
    DMFilePtr dmf2;
};

TEST_F(DMFileDebugTest, CompressedFileSeekTest)
try
{
    // Verify that compression and checksum settings are configured correctly
    ASSERT_EQ(dbContext().getSettingsRef().dt_compression_method.get(), CompressionMethod::LZ4);
    ASSERT_EQ(dbContext().getSettingsRef().dt_checksum_algorithm.get(), ChecksumAlgo::XXH3);
    ASSERT_TRUE(dm_file->getConfiguration().has_value());
    ASSERT_EQ(dm_file->getConfiguration()->getChecksumAlgorithm(), ChecksumAlgo::XXH3);

    // Define columns for testing compressed file operations
    auto cols = std::make_shared<ColumnDefines>();

    // ID column (Int64, not null)
    ColumnDefine id_cd(1, "id", std::make_shared<DataTypeInt64>());
    cols->emplace_back(id_cd);

    // Data column (String, not null) - strings compress well and create multiple blocks
    ColumnDefine data_cd(2, "data", std::make_shared<DataTypeString>());
    cols->emplace_back(data_cd);

    // Large column (Array(Float32), not null) - to ensure multiple compressed blocks
    ColumnDefine large_cd(3, "large_data", std::make_shared<DataTypeArray>(std::make_shared<DataTypeFloat32>()));
    cols->emplace_back(large_cd);

    const size_t total_rows = 500;
    const size_t batch_size = 25; // Small batches to create multiple compressed blocks
    const size_t num_batches = total_rows / batch_size;
    const size_t array_size = 100; // Size of float array per row

    // Write data in multiple batches to create multiple compressed blocks
    {
        auto stream = std::make_shared<DMFileBlockOutputStream>(dbContext(), dm_file, *cols);
        stream->writePrefix();

        for (size_t batch = 0; batch < num_batches; ++batch)
        {
            size_t batch_start = batch * batch_size;
            size_t batch_end = (batch + 1) * batch_size;

            Block block;

            // Create ID column data
            std::vector<Int64> id_data;
            id_data.reserve(batch_size);
            for (size_t i = batch_start; i < batch_end; ++i)
            {
                id_data.push_back(static_cast<Int64>(i));
            }
            block.insert(DB::tests::createColumn<Int64>(id_data, id_cd.name, id_cd.id));

            // Create string data column - repetitive strings that compress well
            std::vector<String> string_data;
            string_data.reserve(batch_size);
            for (size_t i = batch_start; i < batch_end; ++i)
            {
                // Create repetitive strings to ensure good compression
                String str = "batch_" + std::to_string(batch) + "_row_" + std::to_string(i) + "_";
                str += String(100, 'A' + (i % 26)); // Pad with repeated characters
                string_data.push_back(str);
            }
            block.insert(DB::tests::createColumn<String>(string_data, data_cd.name, data_cd.id));

            // Create large array data to ensure multiple compressed blocks
            std::vector<Array> array_data;
            array_data.reserve(batch_size);
            for (size_t i = batch_start; i < batch_end; ++i)
            {
                Array arr;
                arr.reserve(array_size);
                for (size_t j = 0; j < array_size; ++j)
                {
                    // Create predictable float values
                    arr.push_back(static_cast<Float64>(i * 1000 + j + 0.5));
                }
                array_data.push_back(arr);
            }
            auto array_col
                = DB::tests::createColumn<Array>(std::make_tuple(std::make_shared<DataTypeFloat32>()), array_data);
            array_col.name = large_cd.name;
            array_col.column_id = large_cd.id;
            block.insert(array_col);

            stream->write(block, DMFileBlockOutputStream::BlockProperty{0, 0, 0, 0});
        }

        stream->writeSuffix();
    }

    std::cout << "Finished writing " << total_rows << " rows in " << num_batches << " batches" << std::endl;

    // Test multiple seeks to the same location - this is the core test for the bug
    {
        DMFileBlockInputStreamBuilder builder(dbContext());
        builder.setRowsThreshold(50); // Read in small chunks to trigger more seeks
        auto stream = builder.build(
            dm_file,
            *cols,
            RowKeyRanges{RowKeyRange::newAll(false, 1)},
            std::make_shared<ScanContext>());

        // First pass: Read all data and store expected values
        std::vector<std::tuple<Int64, String, Array>> expected_data;
        expected_data.reserve(total_rows);

        size_t total_rows_read = 0;
        while (Block block = stream->read())
        {
            if (!block)
                break;

            ASSERT_EQ(block.columns(), 3);

            auto id_column = block.getByName("id").column;
            auto data_column = block.getByName("data").column;
            auto large_column = block.getByName("large_data").column;

            for (size_t i = 0; i < block.rows(); ++i)
            {
                Int64 id_value = id_column->getInt(i);
                String data_value = data_column->getDataAt(i).toString();

                // Extract array data using Field operator
                Field array_field = large_column->operator[](i);
                Array array_value = DB::get<Array>(array_field);

                expected_data.emplace_back(id_value, data_value, array_value);
            }

            total_rows_read += block.rows();
        }

        ASSERT_EQ(total_rows_read, total_rows);
        ASSERT_EQ(expected_data.size(), total_rows);

        std::cout << "First pass: Successfully read " << total_rows_read << " rows" << std::endl;
    }

    // Now test multiple seeks to the same location - this is the core bug test
    // We'll use DMFileBlockInputStream multiple times to test seeking behavior
    {
        std::cout << "Starting multiple seek test..." << std::endl;

        const size_t seek_iterations = 5;
        std::vector<std::vector<Field>> first_read_data;

        // First read to establish baseline
        {
            DMFileBlockInputStreamBuilder builder(dbContext());
            builder.setRowsThreshold(50); // Small chunks to trigger more seeks
            auto stream = builder.build(
                dm_file,
                *cols,
                RowKeyRanges{RowKeyRange::newAll(false, 1)},
                std::make_shared<ScanContext>());

            // Read only the first block for comparison
            Block first_block = stream->read();
            ASSERT_TRUE(first_block) << "Failed to read first block";

            std::cout << "First read: Got block with " << first_block.rows() << " rows" << std::endl;

            // Store the first few rows for comparison
            size_t rows_to_compare = std::min(first_block.rows(), static_cast<size_t>(10));
            for (size_t row_idx = 0; row_idx < rows_to_compare; ++row_idx)
            {
                std::vector<Field> row_data;
                for (size_t col_idx = 0; col_idx < first_block.columns(); ++col_idx)
                {
                    row_data.push_back(first_block.getByPosition(col_idx).column->operator[](row_idx));
                }
                first_read_data.push_back(row_data);
            }
        }

        ASSERT_FALSE(first_read_data.empty()) << "Failed to read any data in first pass";

        // Now perform multiple reads and verify consistency
        for (size_t iteration = 0; iteration < seek_iterations; ++iteration)
        {
            std::cout << "Seek iteration " << (iteration + 1) << "/" << seek_iterations << std::endl;

            DMFileBlockInputStreamBuilder builder(dbContext());
            builder.setRowsThreshold(50); // Same settings as first read
            auto stream = builder.build(
                dm_file,
                *cols,
                RowKeyRanges{RowKeyRange::newAll(false, 1)},
                std::make_shared<ScanContext>());

            // Read the first block and compare with baseline
            Block block = stream->read();
            ASSERT_TRUE(block) << "Failed to read block in iteration " << iteration;

            ASSERT_EQ(block.columns(), 3) << "Column count mismatch in iteration " << iteration;

            // Compare the first few rows with the baseline
            size_t rows_to_compare = std::min(block.rows(), first_read_data.size());
            for (size_t row_idx = 0; row_idx < rows_to_compare; ++row_idx)
            {
                for (size_t col_idx = 0; col_idx < block.columns(); ++col_idx)
                {
                    Field current_field = block.getByPosition(col_idx).column->operator[](row_idx);
                    Field expected_field = first_read_data[row_idx][col_idx];

                    ASSERT_EQ(current_field, expected_field)
                        << "Data mismatch at row " << row_idx << " column " << col_idx << " iteration " << iteration;
                }
            }
        }

        std::cout << "Successfully completed " << seek_iterations << " seek iterations with consistent results"
                  << std::endl;
    }
}
CATCH

} // namespace DB::DM::tests
